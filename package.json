{"name": "hieltech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@firebase/app": "^0.11.3", "@firebase/auth": "^1.9.1", "@firebase/firestore": "^4.7.10", "firebase": "^11.5.0", "framer-motion": "^12.6.2", "next": "^15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-firebase-hooks": "^5.1.1", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}