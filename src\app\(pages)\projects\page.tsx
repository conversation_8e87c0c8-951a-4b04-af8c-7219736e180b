"use client";

import React from "react";
import Link from "next/link";

interface ProjectCardProps {
  title: string;
  description: string;
  technologies: string[];
  gradient: string;
  androidLink?: string;
  windowsLink?: string;
  link?: string;
  status: "completed" | "in-progress" | "upcoming";
  year: string;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  description,
  technologies,
  gradient,
  androidLink,
  windowsLink,
  link,
  status,
  year,
}) => {
  // Get initials for the placeholder
  const initials = title
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <div className={`h-56 relative overflow-hidden bg-gradient-to-br ${gradient}`}>
        <div className="absolute inset-0 flex items-center justify-center text-white text-4xl font-bold">
          {initials}
        </div>
      </div>
      <div className="p-6">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-semibold">{title}</h3>
          <span className="text-sm text-gray-500 dark:text-gray-400">{year}</span>
        </div>
        <div className="mb-4">
          <span className={`inline-block px-2 py-1 text-xs rounded-full ${
            status === "completed" 
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
              : status === "in-progress" 
              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" 
              : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
          }`}>
            {status === "completed" 
              ? "Completed" 
              : status === "in-progress" 
              ? "In Progress" 
              : "Upcoming"}
          </span>
        </div>
        <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
          {description}
        </p>
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {technologies.map((tech, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-xs"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
        {(androidLink || windowsLink) && (
          <div className="flex gap-4 mb-4">
            {androidLink && (
              <a
                href={androidLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
              >
                Android →
              </a>
            )}
            {windowsLink && (
              <a
                href={windowsLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
              >
                Windows →
              </a>
            )}
          </div>
        )}
        {link && !androidLink && !windowsLink && (
          <a
            href={link}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
          >
            View Project →
          </a>
        )}
      </div>
    </div>
  );
};

export default function ProjectsPage() {
  const projects: ProjectCardProps[] = [
    {
      title: "HielCompta",
      description: "A comprehensive Moroccan accounting application designed to streamline financial management for businesses. Features include invoice generation, expense tracking, financial reporting, and tax calculation specific to Moroccan regulations. Fully offline application available on Google Play Store and GitHub for Windows.",
      technologies: ["Flutter", "Dart", "Firebase", "Cloud Firestore"],
      gradient: "from-purple-500 to-indigo-600",
      androidLink: "https://play.google.com/store/apps/details?id=com.hieltech.moroccanaccounting&pli=1",
      windowsLink: "https://github.com/serhabdel/hielcompta-public/releases",
      status: "completed",
      year: "2024-2025",
    },
    {
      title: "Agevolami.ma",
      description: "A professional website for a Moroccan consulting firm specializing in business advisory services. The site features service descriptions, team profiles, client testimonials, and a contact system for potential clients.",
      technologies: ["React", "Next.js", "Tailwind CSS", "Vercel"],
      gradient: "from-blue-500 to-teal-400",
      link: "https://agevolami.ma",
      status: "completed",
      year: "2025",
    },
    {
      title: "HielMailing",
      description: "An advanced bulk mailing application with AI-powered features for optimizing email campaigns. The system includes email template creation, audience segmentation, scheduling, analytics, and AI-driven content suggestions.",
      technologies: ["Node.js", "React", "MongoDB", "AI/ML", "AWS"],
      gradient: "from-orange-400 to-pink-500",
      status: "in-progress",
      year: "2025",
    },
    {
      title: "Hiel Tech Website",
      description: "The official website for Hiel Tech, showcasing our services, projects, and expertise. Features a modern design with animated backgrounds, responsive layouts, and interactive elements.",
      technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion"],
      gradient: "from-blue-600 to-violet-500",
      link: "/",
      status: "completed",
      year: "2025",
    },
  ];

  const categories = ["All", "Web", "Mobile", "Design", "AI"];
  const [activeCategory, setActiveCategory] = React.useState("All");

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
              Our <span className="text-blue-600 dark:text-blue-400">Projects</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Explore our portfolio of innovative solutions across various technologies and industries
            </p>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-5xl mx-auto">
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    activeCategory === category
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 mb-16">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {projects.map((project, index) => (
                <ProjectCard key={index} {...project} />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50 mt-auto">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Have a Project in Mind?</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Let's discuss how we can help bring your ideas to life with our expertise in software and website development.
            </p>
            <Link 
              href="/contact" 
              className="px-8 py-3 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
            >
              Start a Project
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
