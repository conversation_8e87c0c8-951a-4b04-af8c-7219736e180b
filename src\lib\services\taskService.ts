import { db } from '@/lib/firebase';
import { collection, doc, addDoc, updateDoc, deleteDoc, getDocs, query, where, orderBy } from 'firebase/firestore';
import { Task, KanbanColumn, CalendarEvent, GanttTask } from '@/lib/types/task';

const TASKS_COLLECTION = 'tasks';

export const taskService = {
  // Create a new task
  async createTask(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const taskData = {
        ...task,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      const docRef = await addDoc(collection(db, TASKS_COLLECTION), taskData);
      return { id: docRef.id, ...taskData };
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },

  // Update an existing task
  async updateTask(taskId: string, updates: Partial<Task>) {
    try {
      const taskRef = doc(db, TASKS_COLLECTION, taskId);
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };
      await updateDoc(taskRef, updateData);
      return { id: taskId, ...updateData };
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  },

  // Delete a task
  async deleteTask(taskId: string) {
    try {
      await deleteDoc(doc(db, TASKS_COLLECTION, taskId));
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  },

  // Get tasks for a specific user
  async getUserTasks(userId: string): Promise<Task[]> {
    try {
      const q = query(
        collection(db, TASKS_COLLECTION),
        where('createdBy', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Task[];
    } catch (error) {
      console.error('Error getting user tasks:', error);
      throw error;
    }
  },

  // Get tasks for a specific user
  async getTasks(userId: string): Promise<Task[]> {
    try {
      return await this.getUserTasks(userId);
    } catch (error) {
      console.error('Error getting tasks:', error);
      throw error;
    }
  },

  // Get tasks for Kanban view
  async getKanbanTasks(userId: string): Promise<KanbanColumn[]> {
    try {
      const tasks = await this.getUserTasks(userId);
      const columns: KanbanColumn[] = [
        { id: 'todo', title: 'To Do', tasks: [] },
        { id: 'in-progress', title: 'In Progress', tasks: [] },
        { id: 'done', title: 'Done', tasks: [] }
      ];
      
      tasks.forEach(task => {
        const column = columns.find(col => col.id === task.status);
        if (column) {
          column.tasks.push(task);
        }
      });
      
      return columns;
    } catch (error) {
      console.error('Error getting Kanban tasks:', error);
      throw error;
    }
  },

  // Get tasks for Calendar view
  async getCalendarEvents(userId: string): Promise<CalendarEvent[]> {
    try {
      const tasks = await this.getUserTasks(userId);
      return tasks.map(task => ({
        ...task,
        allDay: false
      }));
    } catch (error) {
      console.error('Error getting calendar events:', error);
      throw error;
    }
  },

  // Get tasks for Gantt view
  async getGanttTasks(userId: string): Promise<GanttTask[]> {
    try {
      const tasks = await this.getUserTasks(userId);
      return tasks.map(task => ({
        ...task,
        dependencies: [],
        progress: 0,
        duration: 1
      }));
    } catch (error) {
      console.error('Error getting Gantt tasks:', error);
      throw error;
    }
  }
};