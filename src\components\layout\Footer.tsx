'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();
  
  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine which logo to use based on theme
  const logoSrc = !mounted ? '/logo-dark.svg' : 
                  theme === 'dark' || theme === 'circuit' || theme === 'white' ? '/logo-white.svg' : '/logo-dark.svg';
  
  return (
    <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="flex items-center">
              <div className="relative h-8 w-8">
                <Image 
                  src={logoSrc} 
                  alt="Hiel Tech Logo" 
                  width={32} 
                  height={32}
                />
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">Hiel Tech</span>
            </Link>
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              Specializing in software and website development and maintenance.
            </p>
          </div>
          
          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider">
              Quick Links
            </h3>
            <ul className="mt-4 space-y-2">
              {[
                { href: '/', label: 'Home' },
                { href: '/about', label: 'About Us' },
                { href: '/projects', label: 'Projects' },
                { href: '/services', label: 'Services' },
              ].map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Services */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider">
              Services
            </h3>
            <ul className="mt-4 space-y-2">
              {[
                { href: '/services#web-development', label: 'Web Development' },
                { href: '/services#software-development', label: 'Software Development' },
                { href: '/services#maintenance', label: 'Maintenance' },
                { href: '/services#consulting', label: 'Consulting' },
              ].map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Contact */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider">
              Contact
            </h3>
            <ul className="mt-4 space-y-2">
              <li className="text-sm text-gray-600 dark:text-gray-400">
                Email: <EMAIL>
              </li>
              <li className="text-sm text-gray-600 dark:text-gray-400">
                Phone: +****************
              </li>
              <li className="mt-4">
                <div className="flex space-x-4">
                  {/* Social Media Icons */}
                  {['twitter', 'linkedin', 'github', 'instagram'].map((social) => (
                    <a 
                      key={social}
                      href={`https://${social}.com/hieltech`} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
                    >
                      <span className="sr-only">{social}</span>
                      <div className="h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        {social[0].toUpperCase()}
                      </div>
                    </a>
                  ))}
                </div>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-800">
          <p className="text-sm text-center text-gray-500 dark:text-gray-400">
            &copy; {currentYear} Hiel Tech. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
