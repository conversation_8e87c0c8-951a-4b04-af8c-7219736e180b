export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'done';
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  startDate: string;
  assignedTo: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface KanbanColumn {
  id: string;
  title: string;
  tasks: Task[];
}

export interface CalendarEvent extends Task {
  allDay: boolean;
}

export interface GanttTask extends Task {
  dependencies: string[];
  progress: number;
  duration: number; // in days
}