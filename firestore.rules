rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    match /tasks/{taskId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.createdBy;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.createdBy && 
        request.resource.data.keys().hasAll(['title', 'description', 'status', 'priority', 'dueDate', 'createdBy']);
      allow update: if request.auth != null && 
        request.auth.uid == resource.data.createdBy && 
        request.resource.data.diff(resource.data).affectedKeys()
          .hasAny(['title', 'description', 'status', 'priority', 'dueDate', 'updatedAt']);
      allow delete: if request.auth != null && request.auth.uid == resource.data.createdBy;
    }
  }
}