'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import StarBackground from './StarBackground';
import CircuitBackground from './CircuitBackground';
import WhiteBackground from './WhiteBackground';

const BackgroundManager = () => {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  // Render the appropriate background based on theme
  if (theme === 'circuit') {
    return <CircuitBackground />;
  } else if (theme === 'white') {
    return <WhiteBackground />;
  }
  
  // Default to star background for light and dark themes
  return <StarBackground />;
};

export default BackgroundManager;
