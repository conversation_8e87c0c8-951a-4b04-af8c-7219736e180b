{"indexes": [{"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}], "fieldOverrides": []}