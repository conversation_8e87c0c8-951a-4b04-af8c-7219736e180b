'use client';

import { useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';

const CircuitBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas to full screen
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    // Create grid points
    const gridSize = 50;
    const points: { x: number; y: number }[] = [];
    
    for (let x = 0; x < canvas.width; x += gridSize) {
      for (let y = 0; y < canvas.height; y += gridSize) {
        // Add some randomness to grid
        if (Math.random() > 0.75) {
          points.push({
            x: x + (Math.random() * 20 - 10),
            y: y + (Math.random() * 20 - 10)
          });
        }
      }
    }

    // Determine colors based on theme
    const getColors = () => {
      if (theme === 'dark') {
        return {
          line: 'rgba(60, 60, 60, 0.4)',
          node: 'rgba(100, 100, 100, 0.6)',
          highlight: 'rgba(255, 255, 255, 0.8)'
        };
      } else {
        return {
          line: 'rgba(220, 220, 220, 0.5)',
          node: 'rgba(180, 180, 180, 0.7)',
          highlight: 'rgba(0, 0, 0, 0.8)'
        };
      }
    };

    // Animation variables
    let animationFrameId: number;
    let highlightNodes: number[] = [];
    let animationProgress = 0;
    
    // Randomly select nodes to highlight
    const updateHighlightNodes = () => {
      if (Math.random() > 0.95 || highlightNodes.length === 0) {
        const startNode = Math.floor(Math.random() * points.length);
        highlightNodes = [startNode];
        
        // Create a path of connected nodes
        for (let i = 0; i < 5; i++) {
          const lastNode = highlightNodes[highlightNodes.length - 1];
          const nearbyNodes = [];
          
          // Find nearby nodes
          for (let j = 0; j < points.length; j++) {
            if (highlightNodes.includes(j)) continue;
            
            const dist = Math.sqrt(
              Math.pow(points[j].x - points[lastNode].x, 2) + 
              Math.pow(points[j].y - points[lastNode].y, 2)
            );
            
            if (dist < gridSize * 2) {
              nearbyNodes.push(j);
            }
          }
          
          if (nearbyNodes.length > 0) {
            highlightNodes.push(nearbyNodes[Math.floor(Math.random() * nearbyNodes.length)]);
          } else {
            break;
          }
        }
        
        animationProgress = 0;
      }
    };
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const colors = getColors();
      
      // Draw connections between points
      ctx.strokeStyle = colors.line;
      ctx.lineWidth = 1;
      
      for (let i = 0; i < points.length; i++) {
        for (let j = i + 1; j < points.length; j++) {
          const dist = Math.sqrt(
            Math.pow(points[j].x - points[i].x, 2) + 
            Math.pow(points[j].y - points[i].y, 2)
          );
          
          if (dist < gridSize * 1.5) {
            ctx.beginPath();
            ctx.moveTo(points[i].x, points[i].y);
            ctx.lineTo(points[j].x, points[j].y);
            ctx.stroke();
          }
        }
      }
      
      // Draw highlight path
      if (highlightNodes.length > 1) {
        const highlightLength = Math.min(
          Math.floor(animationProgress * highlightNodes.length),
          highlightNodes.length - 1
        );
        
        if (highlightLength > 0) {
          ctx.strokeStyle = colors.highlight;
          ctx.lineWidth = 2;
          
          for (let i = 0; i < highlightLength; i++) {
            const current = points[highlightNodes[i]];
            const next = points[highlightNodes[i + 1]];
            
            ctx.beginPath();
            ctx.moveTo(current.x, current.y);
            ctx.lineTo(next.x, next.y);
            ctx.stroke();
          }
        }
        
        animationProgress += 0.02;
        if (animationProgress > 1.2) {
          updateHighlightNodes();
        }
      } else {
        updateHighlightNodes();
      }
      
      // Draw points
      for (let i = 0; i < points.length; i++) {
        const isHighlighted = highlightNodes.includes(i);
        
        ctx.beginPath();
        ctx.arc(points[i].x, points[i].y, isHighlighted ? 3 : 2, 0, Math.PI * 2);
        ctx.fillStyle = isHighlighted ? colors.highlight : colors.node;
        ctx.fill();
      }
      
      animationFrameId = requestAnimationFrame(animate);
    };
    
    animate();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationFrameId);
    };
  }, [theme]);
  
  return (
    <canvas 
      ref={canvasRef}
      className="fixed top-0 left-0 w-full h-full -z-10 pointer-events-none"
      aria-hidden="true"
    />
  );
};

export default CircuitBackground;
