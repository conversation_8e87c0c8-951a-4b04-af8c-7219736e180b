'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { taskService } from '@/lib/services/taskService';
import { Task } from '@/lib/types/task';
import { motion } from 'framer-motion';
import TaskForm from './TaskForm';

type SortField = 'dueDate' | 'priority' | 'status' | 'title';
type SortOrder = 'asc' | 'desc';

export default function ListView() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('dueDate');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  const [filter, setFilter] = useState('');
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  useEffect(() => {
    if (user) {
      loadTasks();
    }
  }, [user]);

  const loadTasks = async () => {
    if (!user?.uid) return;
    
    try {
      setLoading(true);
      const tasksData = await taskService.getTasks(user.uid);
      setTasks(tasksData);
    } catch (error) {
      console.error('Error loading tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const sortTasks = (a: Task, b: Task) => {
    const order = sortOrder === 'asc' ? 1 : -1;
    
    switch (sortField) {
      case 'dueDate':
        return (new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()) * order;
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return (priorityOrder[a.priority as keyof typeof priorityOrder] -
          priorityOrder[b.priority as keyof typeof priorityOrder]) * order;
      case 'status':
        const statusOrder = { todo: 1, 'in-progress': 2, done: 3 };
        return (statusOrder[a.status as keyof typeof statusOrder] -
          statusOrder[b.status as keyof typeof statusOrder]) * order;
      default:
        return a[sortField].localeCompare(b[sortField]) * order;
    }
  };

  const filteredTasks = tasks
    .filter(task =>
      task.title.toLowerCase().includes(filter.toLowerCase()) ||
      task.description.toLowerCase().includes(filter.toLowerCase())
    )
    .sort(sortTasks);

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'medium':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      default:
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'done':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'in-progress':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const handleTaskSubmit = () => {
    loadTasks();
    setShowTaskForm(false);
    setEditingTask(null);
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setShowTaskForm(true);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full relative"
    >
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between space-x-4">
            <input
              type="text"
              placeholder="Search tasks..."
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="flex-1 px-4 py-2 rounded-lg bg-white/10 dark:bg-gray-800/30 border border-white/20 dark:border-gray-700/30 backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300"
            />
            <button
              onClick={() => setShowTaskForm(true)}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600/90 hover:bg-blue-700/90 rounded-md backdrop-blur-sm transition-all duration-300 shadow-lg hover:shadow-blue-500/20"
            >
              Add Task
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full backdrop-blur-md bg-white/10 dark:bg-gray-800/30 rounded-lg shadow-xl border border-white/20 dark:border-gray-700/30">
              <thead>
                <tr className="text-left border-b border-gray-200 dark:border-gray-700">
                  <th
                    className="p-4 cursor-pointer hover:bg-white/5 dark:hover:bg-gray-700/50"
                    onClick={() => handleSort('title')}
                  >
                    Title {sortField === 'title' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </th>
                  <th
                    className="p-4 cursor-pointer hover:bg-white/5 dark:hover:bg-gray-700/50"
                    onClick={() => handleSort('status')}
                  >
                    Status {sortField === 'status' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </th>
                  <th
                    className="p-4 cursor-pointer hover:bg-white/5 dark:hover:bg-gray-700/50"
                    onClick={() => handleSort('priority')}
                  >
                    Priority {sortField === 'priority' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </th>
                  <th
                    className="p-4 cursor-pointer hover:bg-white/5 dark:hover:bg-gray-700/50"
                    onClick={() => handleSort('dueDate')}
                  >
                    Due Date {sortField === 'dueDate' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredTasks.map((task) => (
                  <motion.tr
                    key={task.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border-b border-white/10 dark:border-gray-700/30 hover:bg-white/5 dark:hover:bg-gray-700/30 transition-all duration-300"
                  >
                    <td className="p-4">
                      <div className="font-medium text-gray-800 dark:text-gray-200">
                        {task.title}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {task.description}
                      </div>
                    </td>
                    <td className="p-4">
                      <span className={`text-xs px-2 py-1 rounded ${getStatusColor(task.status)}`}>
                        {task.status}
                      </span>
                    </td>
                    <td className="p-4">
                      <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </span>
                    </td>
                    <td className="p-4 text-gray-600 dark:text-gray-400">
                      {new Date(task.dueDate).toLocaleDateString()}
                    </td>
                    <td className="p-4">
                      <button
                        onClick={() => handleEditTask(task)}
                        className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Edit
                      </button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {showTaskForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md">
            <TaskForm
              onSubmit={handleTaskSubmit}
              onCancel={() => {
                setShowTaskForm(false);
                setEditingTask(null);
              }}
              initialData={editingTask || undefined}
              mode={editingTask ? 'edit' : 'create'}
            />
          </div>
        </div>
      )}
    </motion.div>
  );
}