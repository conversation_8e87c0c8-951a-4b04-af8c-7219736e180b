import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 lg:py-36 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            {/* Banner Image */}
            <div className="mb-10 flex justify-center animate-fade-in">
              <div className="relative w-full max-w-lg h-24 md:h-32">
                <Image
                  src="/hiel-tech-banner.svg"
                  alt="Hiel Tech Banner"
                  fill
                  priority
                  className="object-contain"
                />
              </div>
            </div>
            
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight animate-fade-in">
              Transforming Ideas into <span className="text-blue-600 dark:text-blue-400">Digital Reality</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Hiel Tech specializes in crafting exceptional software and websites that drive business growth and enhance user experiences.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center animate-slide-up" style={{ animationDelay: "0.2s" }}>
              <Link 
                href="/services" 
                className="px-8 py-3 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
              >
                Our Services
              </Link>
              <Link 
                href="/projects" 
                className="px-8 py-3 rounded-full border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                View Projects
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">Our Services</h2>
            <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We provide comprehensive solutions for all your digital needs
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Web Development",
                description: "Custom websites that are fast, responsive, and optimized for search engines.",
                icon: "🌐",
              },
              {
                title: "Software Development",
                description: "Tailored software solutions to automate and streamline your business processes.",
                icon: "💻",
              },
              {
                title: "Maintenance & Support",
                description: "Ongoing support and maintenance to keep your digital assets running smoothly.",
                icon: "🛠️",
              },
            ].map((service, index) => (
              <div 
                key={index} 
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="text-3xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">{service.description}</p>
                <Link 
                  href={`/services#${service.title.toLowerCase().replace(/\s+/g, '-')}`}
                  className="mt-4 inline-block text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Learn more →
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">Featured Projects</h2>
            <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Take a look at some of our recent work
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "HielCompta",
                description: "A comprehensive Moroccan accounting application designed to streamline financial management for businesses. Fully offline application available on Google Play Store and GitHub for Windows.",
                gradient: "from-purple-500 to-indigo-600",
                initials: "HC",
                androidLink: "https://play.google.com/store/apps/details?id=com.hieltech.moroccanaccounting&pli=1",
                windowsLink: "https://github.com/serhabdel/hielcompta-public/releases",
                year: "2024-2025"
              },
              {
                title: "Agevolami.ma",
                description: "A professional website for a Moroccan consulting firm specializing in business advisory services.",
                gradient: "from-blue-500 to-teal-400",
                initials: "AM",
                link: "https://agevolami.ma",
                year: "2025"
              },
              {
                title: "HielMailing",
                description: "An advanced bulk mailing application with AI-powered features for optimizing email campaigns.",
                gradient: "from-orange-400 to-pink-500",
                initials: "HM",
                year: "2025"
              },
            ].map((project, index) => (
              <div 
                key={index} 
                className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
              >
                <div className={`h-48 bg-gradient-to-br ${project.gradient} relative`}>
                  <div className="absolute inset-0 flex items-center justify-center text-white text-4xl font-bold">
                    {project.initials}
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-semibold">{project.title}</h3>
                    <span className="text-sm text-gray-500 dark:text-gray-400">{project.year}</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{project.description}</p>
                  
                  {project.title === "HielCompta" && (
                    <div className="flex gap-4">
                      <a 
                        href={project.androidLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        Android →
                      </a>
                      <a 
                        href={project.windowsLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        Windows →
                      </a>
                    </div>
                  )}
                  
                  {project.link && (
                    <a 
                      href={project.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      View project →
                    </a>
                  )}
                  
                  {!project.link && project.title !== "HielCompta" && (
                    <Link 
                      href="/projects"
                      className="text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Learn more →
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link 
              href="/projects" 
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>

      {/* About Founder */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold">Meet Our Founder</h2>
              <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                The vision and expertise behind Hiel Tech
              </p>
            </div>
            
            <div className="md:flex items-center gap-12">
              <div className="md:w-1/3 mb-8 md:mb-0 flex justify-center">
                <div className="w-48 h-48 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-3xl font-bold">
                  AS
                </div>
              </div>
              <div className="md:w-2/3">
                <h3 className="text-2xl font-semibold mb-2">Abdelhalim Serhani</h3>
                <p className="text-blue-600 dark:text-blue-400 mb-4">Founder & Developer</p>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Abdelhalim is a Management Controller with expertise in finance and work automation. His passion for technology and finance led him to create Hiel Tech, where he combines his financial knowledge with technical skills to develop innovative solutions.
                </p>
                <Link 
                  href="/about"
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Learn more about our founder →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials - Commented out as there are no real testimonials 
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">What Our Clients Say</h2>
            <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We take pride in delivering exceptional results for our clients
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                quote: "Hiel Tech transformed our online presence with a beautiful, functional website that has significantly increased our customer engagement.",
                author: "Jane Smith",
                position: "CEO, TechCorp",
              },
              {
                quote: "The custom software solution developed by Hiel Tech has streamlined our operations and saved us countless hours of manual work.",
                author: "John Doe",
                position: "CTO, InnovateX",
              },
            ].map((testimonial, index) => (
              <div 
                key={index} 
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm"
              >
                <div className="flex flex-col h-full">
                  <blockquote className="flex-grow">
                    <p className="text-gray-600 dark:text-gray-300 italic">"{testimonial.quote}"</p>
                  </blockquote>
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <p className="font-semibold">{testimonial.author}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{testimonial.position}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      */}

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="bg-blue-600 dark:bg-blue-700 rounded-2xl p-8 md:p-12 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Your Project?</h2>
            <p className="text-blue-100 max-w-2xl mx-auto mb-8">
              Let's discuss how we can help bring your ideas to life with our expertise in software and website development.
            </p>
            <Link 
              href="/contact" 
              className="px-8 py-3 rounded-full bg-white text-blue-600 font-medium hover:bg-gray-100 transition-colors"
            >
              Get in Touch
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
